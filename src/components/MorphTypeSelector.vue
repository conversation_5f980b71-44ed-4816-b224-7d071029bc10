<template>
  <multi-select-tags
    v-model="selectedValues"
    :options="filteredMorphTypes"
    option-key="ID"
    option-label="morph_name"
    placeholder="请选择型态类型"
    :disabled="disabled"
    @change="handleChange"
  />
</template>

<script>
import MultiSelectTags from './MultiSelectTags.vue'
import { getFilteredMorphTypes } from '@/utils/morphTypeUtils'

export default {
  name: 'MorphTypeSelector',
  components: {
    MultiSelectTags
  },
  props: {
    // 当前选中的值
    value: {
      type: Array,
      default: () => []
    },
    // 所有型态选项列表
    morphTypes: {
      type: Array,
      default: () => []
    },
    // 平台信息（用于过滤）
    platform: {
      type: Object,
      default: null
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    selectedValues: {
      get() {
        return this.value || []
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    // 根据平台过滤后的型态选项
    filteredMorphTypes() {
      return getFilteredMorphTypes(this.morphTypes, this.platform)
    }
  },
  methods: {
    handleChange(values) {
      this.$emit('change', values)
    }
  }
}
</script>
