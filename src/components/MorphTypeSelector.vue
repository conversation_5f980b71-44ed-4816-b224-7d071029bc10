<template>
  <el-select
    v-model="selectedValue"
    placeholder="请选择型态类型"
    :disabled="disabled"
    clearable
    style="width: 100%"
    @change="handleChange"
  >
    <el-option
      v-for="morphType in filteredMorphTypes"
      :key="morphType.ID"
      :label="morphType.morph_name"
      :value="morphType.ID"
    >
    </el-option>
  </el-select>
</template>

<script>
import { getFilteredMorphTypes } from '@/utils/morphTypeUtils'

export default {
  name: 'MorphTypeSelector',
  props: {
    // 当前选中的值 - 改为单个值而不是数组
    value: {
      type: [String, Number],
      default: null
    },
    // 所有型态选项列表
    morphTypes: {
      type: Array,
      default: () => []
    },
    // 平台信息（用于过滤）
    platform: {
      type: Object,
      default: null
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    selectedValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    // 根据平台过滤后的型态选项
    filteredMorphTypes() {
      return getFilteredMorphTypes(this.morphTypes, this.platform)
    }
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    }
  }
}
</script>
